{"tasks": [{"id": "4917ed13-9808-4af4-a9f1-a01940ca17a0", "name": "创建纯任务汇总查询方法", "description": "在 FfsafeScantaskSummaryMapper 中添加只查询 ffsafe_scantask_summary 表的新方法，避免LEFT JOIN导致的一对多数据重复问题。该方法将直接用于 createBatchTaskReport 方法中替代有问题的查询。", "notes": "此方法是解决一对多关系问题的核心，必须确保SQL查询的正确性和性能。命名使用 'ByIdsOnly' 后缀明确表示只查询单表。", "status": "pending", "dependencies": [], "createdAt": "2025-08-20T04:57:54.218Z", "updatedAt": "2025-08-20T04:57:54.218Z", "relatedFiles": [{"path": "aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/service/mapper/FfsafeScantaskSummaryMapper.java", "type": "TO_MODIFY", "description": "添加新的查询方法接口定义", "lineStart": 24, "lineEnd": 25}, {"path": "aqsoc-monitor/src/main/resources/mapper/ffsafe/FfsafeScantaskSummaryMapper.xml", "type": "TO_MODIFY", "description": "添加对应的SQL查询实现", "lineStart": 88, "lineEnd": 89}], "implementationGuide": "1. 在 FfsafeScantaskSummaryMapper.java 接口中添加新方法：\\n```java\\n/**\\n * 批量查询非凡扫描任务汇总（仅查询任务汇总表，不关联报告记录）\\n * @param ids 非凡扫描任务汇总主键集合\\n * @return 非凡扫描任务汇总集合\\n */\\nList<FfsafeScantaskSummary> selectFfsafeScantaskSummaryByIdsOnly(Long[] ids);\\n```\\n\\n2. 在 FfsafeScantaskSummaryMapper.xml 中添加对应的SQL查询：\\n```xml\\n<select id=\\\"selectFfsafeScantaskSummaryByIdsOnly\\\" parameterType=\\\"Long\\\" resultType=\\\"FfsafeScantaskSummary\\\">\\n    select \\n        id, job_id, task_id, task_type, task_status, finish_rate, \\n        high_risk_num, middle_risk_num, low_risk_num, poc_risk_num, info_risk_num, \\n        start_time, end_time\\n    from ffsafe_scantask_summary\\n    where id in\\n    <foreach item=\\\"id\\\" collection=\\\"array\\\" open=\\\"(\\\" separator=\\\",\\\" close=\\\")\\\">\\n        #{id}\\n    </foreach>\\n    ORDER BY id\\n</select>\\n```\\n\\n3. 确保SQL只查询单表，避免任何JOIN操作，返回纯净的任务汇总数据。", "verificationCriteria": "1. 新方法能够正确查询指定ID的任务汇总记录\\n2. 查询结果数量与输入ID数量严格一对一匹配\\n3. 不包含任何报告相关字段，只返回任务汇总基本信息\\n4. SQL执行性能良好，无多余的JOIN操作", "analysisResult": "分析并优化 ScanTaskReport 类中 createBatchTaskReport 方法的校验逻辑问题。核心问题是SQL查询使用LEFT JOIN导致一对多关系返回多行数据，校验逻辑错误地假设了一对一关系。采用简化解决方案：直接在 createBatchTaskReport 方法中使用新的纯任务查询方法，重用现有的重复报告检查功能，避免修改未使用的 validateBatchReportGenerationAndGet 方法。"}, {"id": "8011c1f3-d73f-41ad-aa8b-2d4f763a036c", "name": "在Service层添加纯任务查询和验证方法", "description": "在 IFfsafeScantaskSummaryService 接口和实现类中添加对应的业务方法，封装新的纯任务查询逻辑，并包含任务完成状态验证，提供给 createBatchTaskReport 方法调用。", "notes": "Service层方法需要包含完整的任务状态验证逻辑，包括完成率和状态检查，保持与现有代码风格一致。", "status": "pending", "dependencies": [{"taskId": "4917ed13-9808-4af4-a9f1-a01940ca17a0"}], "createdAt": "2025-08-20T04:57:54.218Z", "updatedAt": "2025-08-20T04:57:54.218Z", "relatedFiles": [{"path": "aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/service/IFfsafeScantaskSummaryService.java", "type": "TO_MODIFY", "description": "添加新的Service接口方法", "lineStart": 86, "lineEnd": 87}, {"path": "aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/service/impl/FfsafeScantaskSummaryServiceImpl.java", "type": "TO_MODIFY", "description": "实现新的Service方法，包含完整的验证逻辑", "lineStart": 194, "lineEnd": 195}], "implementationGuide": "1. 在 IFfsafeScantaskSummaryService.java 接口中添加方法：\\n```java\\n/**\\n * 批量查询并验证扫描任务汇总（仅查询任务汇总表）\\n * @param ids 非凡扫描任务汇总主键集合\\n * @return 验证通过的任务汇总集合\\n * @throws ServiceException 如果任务不存在或状态不满足条件\\n */\\nList<FfsafeScantaskSummary> selectAndValidateTaskSummariesByIds(Long[] ids);\\n```\\n\\n2. 在 FfsafeScantaskSummaryServiceImpl.java 中实现该方法：\\n```java\\n@Override\\npublic List<FfsafeScantaskSummary> selectAndValidateTaskSummariesByIds(Long[] ids) {\\n    try {\\n        log.debug(\\\"查询并验证任务汇总信息，任务数量: {}\\\", ids != null ? ids.length : 0);\\n        \\n        if (ids == null || ids.length == 0) {\\n            throw new ServiceException(\\\"任务ID列表不能为空\\\");\\n        }\\n        \\n        // 使用新的纯任务查询方法\\n        List<FfsafeScantaskSummary> tasks = ffsafeScantaskSummaryMapper.selectFfsafeScantaskSummaryByIdsOnly(ids);\\n        \\n        if (tasks == null || tasks.size() != ids.length) {\\n            throw new ServiceException(\\\"部分任务不存在，请检查任务ID\\\");\\n        }\\n        \\n        // 验证任务完成状态\\n        for (FfsafeScantaskSummary task : tasks) {\\n            if (task.getFinishRate() == null || task.getFinishRate() != 100) {\\n                throw new ServiceException(\\\"任务ID \\\" + task.getId() + \\\" 未完成，无法生成报告\\\");\\n            }\\n            \\n            if (task.getTaskStatus() == null || \\n                (task.getTaskType() == 1 && task.getTaskStatus() != 4) ||  // 主机漏扫\\n                (task.getTaskType() == 2 && task.getTaskStatus() != 2)) {   // Web漏扫\\n                throw new ServiceException(\\\"任务ID \\\" + task.getId() + \\\" 状态不正确，无法生成报告\\\");\\n            }\\n        }\\n        \\n        log.debug(\\\"验证完成，返回记录数: {}\\\", tasks.size());\\n        return tasks;\\n    } catch (ServiceException e) {\\n        throw e;\\n    } catch (Exception e) {\\n        log.error(\\\"查询并验证任务汇总信息时发生异常\\\", e);\\n        throw new ServiceException(\\\"查询任务汇总信息失败: \\\" + e.getMessage());\\n    }\\n}\\n```\\n\\n3. 遵循现有的异常处理和日志记录模式。", "verificationCriteria": "1. Service方法能够正确调用Mapper层的新方法\\n2. 包含完整的参数验证和任务状态验证\\n3. 验证逻辑与原有业务规则一致\\n4. 异常处理和日志记录符合项目规范\\n5. 返回结果格式正确", "analysisResult": "分析并优化 ScanTaskReport 类中 createBatchTaskReport 方法的校验逻辑问题。核心问题是SQL查询使用LEFT JOIN导致一对多关系返回多行数据，校验逻辑错误地假设了一对一关系。采用简化解决方案：直接在 createBatchTaskReport 方法中使用新的纯任务查询方法，重用现有的重复报告检查功能，避免修改未使用的 validateBatchReportGenerationAndGet 方法。"}, {"id": "a1b1af6c-69c6-4bd5-b10d-1f9e363c6982", "name": "重构createBatchTaskReport方法核心逻辑", "description": "重构 ScanTaskReport 类中的 createBatchTaskReport 方法，移除对有问题的 getReportRecordsWithTaskInfoBySummaryIds 方法的依赖，直接使用新的纯任务查询方法和现有的重复报告检查功能，彻底解决一对多关系问题。", "notes": "这是核心重构任务，需要确保业务流程的完整性和事务的一致性。重点是重用现有功能而不是重新实现，同时彻底移除有问题的查询依赖。", "status": "pending", "dependencies": [{"taskId": "8011c1f3-d73f-41ad-aa8b-2d4f763a036c"}], "createdAt": "2025-08-20T04:57:54.218Z", "updatedAt": "2025-08-20T04:57:54.218Z", "relatedFiles": [{"path": "aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/event/ScanTaskReport.java", "type": "TO_MODIFY", "description": "重构createBatchTaskReport方法的核心逻辑，添加辅助方法", "lineStart": 108, "lineEnd": 280}], "implementationGuide": "1. 重构 ScanTaskReport.createBatchTaskReport 方法的核心逻辑：\\n```java\\n@Transactional(rollbackFor = Exception.class)\\npublic boolean createBatchTaskReport(Long[] summaryIds, int taskType, String fileName) throws Exception {\\n    try {\\n        log.info(\\\"开始生成报告，任务汇总ID数量: {}, 任务类型: {}, 文件名: {}\\\",\\n                summaryIds != null ? summaryIds.length : 0, taskType, fileName);\\n\\n        // 1. 参数验证\\n        if (summaryIds == null || summaryIds.length == 0) {\\n            log.warn(\\\"生成报告失败：任务汇总ID列表为空\\\");\\n            throw new ServiceException(\\\"请选择漏扫记录\\\");\\n        }\\n\\n        // 2. 查询并验证任务汇总信息（使用新的纯任务查询方法）\\n        log.debug(\\\"开始查询任务汇总基本信息，任务汇总ID: {}\\\", Arrays.toString(summaryIds));\\n        List<FfsafeScantaskSummary> taskSummaries = ffsafeScantaskSummaryService.selectAndValidateTaskSummariesByIds(summaryIds);\\n        log.info(\\\"成功查询到 {} 条任务汇总记录\\\", taskSummaries.size());\\n\\n        // 3. 检查重复报告（重用现有功能）\\n        Integer generateSource = FfsafeScanReportRecord.GENERATE_SOURCE_BATCH;\\n        if (summaryIds.length == 1) {\\n            log.debug(\\\"单条记录模式，检查重复报告生成\\\");\\n            List<Integer> existingReports = ffsafeScanReportTaskRelationService.checkDuplicateReports(summaryIds);\\n            if (!existingReports.isEmpty()) {\\n                log.error(\\\"任务汇总ID {} 已经生成过报告，请勿重复生成!\\\", summaryIds[0]);\\n                throw new ServiceException(\\\"已经生成过报告，请勿重复生成!\\\");\\n            }\\n            generateSource = FfsafeScanReportRecord.GENERATE_SOURCE_SINGLE;\\n        }\\n\\n        // 4. 构建批量扫描目标信息\\n        log.debug(\\\"开始构建批量扫描目标信息\\\");\\n        String batchScanTarget = buildBatchScanTargetFromSummaries(taskSummaries);\\n        log.info(\\\"批量扫描目标构建完成\\\");\\n\\n        // 5. 提取任务ID用于第三方接口调用\\n        log.debug(\\\"开始提取任务ID用于第三方接口调用\\\");\\n        Integer[] taskIds = extractTaskIdsFromSummaries(taskSummaries);\\n        log.info(\\\"成功提取 {} 个任务ID\\\", taskIds.length);\\n\\n        // 6. 转换报告类型\\n        int reportType = (taskType == 1) ? 2 : 1;\\n\\n        // 7. 调用第三方接口生成报告（后续逻辑保持不变）\\n        Long[] taskIdsLong = Arrays.stream(taskIds).map(Integer::longValue).toArray(Long[]::new);\\n        createTaskReportParam.parseParam(taskIdsLong, reportType, fileName);\\n        CreateTaskReportResult createTaskReportResult = scanTaskService.createTaskReport(createTaskReportParam);\\n        \\n        // ... 后续处理逻辑保持不变\\n    }\\n}\\n```\\n\\n2. 添加必要的辅助方法：\\n```java\\n/**\\n * 从任务汇总列表构建批量扫描目标信息\\n */\\nprivate String buildBatchScanTargetFromSummaries(List<FfsafeScantaskSummary> summaries) {\\n    List<String> targets = new ArrayList<>();\\n    for (FfsafeScantaskSummary summary : summaries) {\\n        try {\\n            if (summary.getJobId() != null) {\\n                SysJob sysJob = sysJobService.selectJobById(summary.getJobId().longValue());\\n                if (sysJob != null && sysJob.getInvokeTarget() != null) {\\n                    String scanTarget = ScanTargetUtils.extractScanTarget(sysJob.getInvokeTarget());\\n                    if (!scanTarget.isEmpty()) {\\n                        targets.add(scanTarget);\\n                    }\\n                }\\n            }\\n        } catch (Exception e) {\\n            log.warn(\\\"获取任务汇总 {} 的扫描目标失败: {}\\\", summary.getId(), e.getMessage());\\n        }\\n    }\\n    return String.join(\\\";\\\", targets);\\n}\\n\\n/**\\n * 从任务汇总列表中提取任务ID\\n */\\nprivate Integer[] extractTaskIdsFromSummaries(List<FfsafeScantaskSummary> summaries) {\\n    return summaries.stream()\\n                   .map(FfsafeScantaskSummary::getTaskId)\\n                   .toArray(Integer[]::new);\\n}\\n```\\n\\n3. 移除对 getReportRecordsWithTaskInfoBySummaryIds 和相关验证方法的调用。", "verificationCriteria": "1. 方法能够正确处理批量和单选报告生成\\n2. 校验逻辑不再因一对多关系而失败\\n3. 重复报告检查功能正常工作\\n4. 事务处理和异常处理保持完整\\n5. 日志记录详细且格式一致\\n6. 扫描目标构建和任务ID提取功能正常\\n7. 与第三方接口的集成保持不变", "analysisResult": "分析并优化 ScanTaskReport 类中 createBatchTaskReport 方法的校验逻辑问题。核心问题是SQL查询使用LEFT JOIN导致一对多关系返回多行数据，校验逻辑错误地假设了一对一关系。采用简化解决方案：直接在 createBatchTaskReport 方法中使用新的纯任务查询方法，重用现有的重复报告检查功能，避免修改未使用的 validateBatchReportGenerationAndGet 方法。"}, {"id": "bce624fb-38f8-4864-947f-4099050fc698", "name": "测试验证和文档更新", "description": "对修复后的功能进行全面测试，验证批量和单选报告生成功能的正确性，特别是验证一对多关系问题已解决，更新相关文档说明修复的问题和新的实现方式。", "notes": "测试是确保修复质量的关键步骤，需要覆盖各种业务场景和边界条件，特别要验证一对多关系问题的彻底解决。", "status": "pending", "dependencies": [{"taskId": "a1b1af6c-69c6-4bd5-b10d-1f9e363c6982"}], "createdAt": "2025-08-20T04:57:54.218Z", "updatedAt": "2025-08-20T04:57:54.218Z", "relatedFiles": [{"path": "aqsoc-monitor/src/test/java/com/ruoyi/ffsafe/scantaskapi", "type": "CREATE", "description": "创建测试用例验证修复效果", "lineStart": 1, "lineEnd": 100}, {"path": "share-docs/tasks/tasks-ScanTaskReport校验逻辑修复.md", "type": "CREATE", "description": "创建修复文档记录问题和解决方案", "lineStart": 1, "lineEnd": 50}], "implementationGuide": "1. 创建测试用例验证修复效果：\\n- 测试单个任务汇总ID的报告生成\\n- 测试多个任务汇总ID的批量报告生成\\n- 测试一个任务汇总关联多个报告记录的场景（重点验证不再出现数量不匹配异常）\\n- 测试重复报告检查功能\\n- 测试任务状态验证功能\\n- 测试不存在的任务ID处理\\n\\n2. 使用数据库中的实际数据进行测试：\\n- 使用ID为799的汇总记录（已知关联多个报告）\\n- 验证不再出现\\\"数量不匹配\\\"的异常\\n- 确认业务逻辑的完整性\\n- 验证扫描目标构建和任务ID提取的正确性\\n\\n3. 性能测试：\\n- 对比修复前后的查询性能\\n- 验证新的纯任务查询的效率\\n- 确认批量处理性能没有下降\\n\\n4. 回归测试：\\n- 确保现有功能不受影响\\n- 验证其他使用相关方法的功能正常\\n- 测试事务回滚机制\\n\\n5. 更新相关文档：\\n- 记录修复的问题和解决方案\\n- 说明新增方法的用途和使用方式\\n- 更新API文档（如果需要）\\n- 创建修复总结文档", "verificationCriteria": "1. 所有测试用例通过，功能正常工作\\n2. 不再出现一对多关系导致的校验错误\\n3. 批量和单选报告生成功能都正常\\n4. 性能测试结果满足要求\\n5. 回归测试确认无副作用\\n6. 文档完整准确\\n7. ID为799的测试用例能够正常处理", "analysisResult": "分析并优化 ScanTaskReport 类中 createBatchTaskReport 方法的校验逻辑问题。核心问题是SQL查询使用LEFT JOIN导致一对多关系返回多行数据，校验逻辑错误地假设了一对一关系。采用简化解决方案：直接在 createBatchTaskReport 方法中使用新的纯任务查询方法，重用现有的重复报告检查功能，避免修改未使用的 validateBatchReportGenerationAndGet 方法。"}]}